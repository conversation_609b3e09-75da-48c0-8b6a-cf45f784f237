// Test script to verify onboarding improvements
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://udnhkdnbvjzcxooukqrq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkbmhrZG5idmp6Y3hvb3VrcXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2Njk1NTYsImV4cCI6MjA2NDI0NTU1Nn0.fUGiIMEf7xk7R0G9EFOjYkJpO3ptkrMYjnwkA-PeOPs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testOnboardingData() {
  console.log('🧪 Testing onboarding data scenarios...\n');

  // Test 1: User with complete employee details
  console.log('📋 Test 1: User with complete employee details');
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'user123'
    });

    if (error) {
      console.log('   ❌ Login failed:', error.message);
    } else {
      console.log('   ✅ Login successful:', data.user.email);
      
      // Check if user has employee details
      const { data: empDetails, error: empError } = await supabase
        .from('employee_details')
        .select('*')
        .eq('user_id', data.user.id)
        .single();

      if (empError) {
        console.log('   ❌ No employee details found - this will trigger the improved onboarding flow');
      } else {
        console.log('   ✅ Employee details found:', empDetails.full_name);
      }

      // Check user preferences
      const { data: prefs, error: prefError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', data.user.id)
        .single();

      if (prefError) {
        console.log('   ❌ No user preferences found - vehicle selection will be shown');
      } else {
        console.log('   ✅ User preferences found, vehicle type:', prefs.vehicle_type || 'None');
      }
    }

    await supabase.auth.signOut();
  } catch (err) {
    console.error('   ❌ Test error:', err.message);
  }

  console.log('\n📋 Test 2: Creating a user without employee details');
  
  // Test 2: Create a user without employee details to test the improved flow
  try {
    const testEmail = '<EMAIL>';
    const testPassword = 'test123';

    // Try to create a new user
    const { data: newUser, error: createError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: { role: 'user' }
      }
    });

    if (createError && !createError.message.includes('already registered')) {
      console.log('   ❌ User creation failed:', createError.message);
    } else {
      console.log('   ✅ User created or already exists');
      
      // Try to sign in
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      });

      if (signInError) {
        console.log('   ❌ Sign in failed:', signInError.message);
      } else {
        console.log('   ✅ Sign in successful - this user will see the improved onboarding');
        console.log('   📝 User will see: "I notice your employee profile isn\'t set up yet..."');
        console.log('   🚗 User will be prompted to select vehicle preference');
        
        await supabase.auth.signOut();
      }
    }
  } catch (err) {
    console.error('   ❌ Test error:', err.message);
  }

  console.log('\n🎉 Onboarding test completed!');
  console.log('\n📋 Summary of improvements:');
  console.log('   ✅ Consolidated user information display');
  console.log('   ✅ Added vehicle preference collection');
  console.log('   ✅ Graceful handling of missing employee details');
  console.log('   ✅ No more repetitive questions');
  console.log('   ✅ Comprehensive feature overview in one message');
}

testOnboardingData().catch(console.error);
