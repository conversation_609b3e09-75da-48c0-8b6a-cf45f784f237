<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Office Assistant - Loading Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        .app-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        button {
            background: #4A80F0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3a70e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Smart Office Assistant - Loading Debug</h1>
        
        <div class="status info">
            <strong>Debug Mode:</strong> This page helps debug loading issues with the Smart Office Assistant app.
        </div>

        <div id="status-container">
            <div class="status warning">
                <strong>Status:</strong> <span id="app-status">Checking app status...</span>
            </div>
        </div>

        <div>
            <button onclick="loadApp()">Load App</button>
            <button onclick="reloadApp()">Reload App</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="checkStatus()">Check Status</button>
        </div>

        <iframe id="app-frame" class="app-frame" src="about:blank"></iframe>

        <div class="logs" id="logs">
            <div class="log-entry log-info">Debug console initialized...</div>
        </div>
    </div>

    <script>
        let logCount = 0;
        const maxLogs = 100;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(entry);
            
            // Keep only the last maxLogs entries
            if (logs.children.length > maxLogs) {
                logs.removeChild(logs.firstChild);
            }
            
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('app-status');
            const statusContainer = document.querySelector('#status-container .status');
            
            statusElement.textContent = message;
            statusContainer.className = `status ${type}`;
            
            log(`Status: ${message}`, type);
        }

        function loadApp() {
            log('Loading Smart Office Assistant...', 'info');
            updateStatus('Loading app...', 'warning');
            
            const frame = document.getElementById('app-frame');
            frame.src = 'http://localhost:8082';
            
            // Set a timeout to check if the app loads
            setTimeout(() => {
                checkAppLoaded();
            }, 5000);
        }

        function reloadApp() {
            log('Reloading app...', 'info');
            const frame = document.getElementById('app-frame');
            frame.src = frame.src;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div class="log-entry log-info">Debug console cleared...</div>';
        }

        function checkStatus() {
            log('Checking app status...', 'info');
            
            // Check if the development server is running
            fetch('http://localhost:8082')
                .then(response => {
                    if (response.ok) {
                        updateStatus('Development server is running', 'success');
                        log('✅ Development server is accessible', 'success');
                    } else {
                        updateStatus('Development server returned error', 'error');
                        log('❌ Development server returned error: ' + response.status, 'error');
                    }
                })
                .catch(error => {
                    updateStatus('Cannot connect to development server', 'error');
                    log('❌ Cannot connect to development server: ' + error.message, 'error');
                });
        }

        function checkAppLoaded() {
            try {
                const frame = document.getElementById('app-frame');
                const frameDoc = frame.contentDocument || frame.contentWindow.document;
                
                if (frameDoc.readyState === 'complete') {
                    const title = frameDoc.title;
                    if (title && title.includes('Smart Office')) {
                        updateStatus('App loaded successfully', 'success');
                        log('✅ App loaded successfully', 'success');
                    } else {
                        updateStatus('App may not have loaded correctly', 'warning');
                        log('⚠️ App may not have loaded correctly', 'warning');
                    }
                } else {
                    updateStatus('App is still loading...', 'warning');
                    log('⏳ App is still loading...', 'warning');
                }
            } catch (error) {
                updateStatus('Cannot access app frame (CORS)', 'warning');
                log('⚠️ Cannot access app frame due to CORS restrictions', 'warning');
            }
        }

        // Auto-check status on page load
        window.onload = function() {
            log('Debug page loaded', 'success');
            checkStatus();
        };

        // Monitor frame loading
        document.getElementById('app-frame').onload = function() {
            log('App frame loaded', 'info');
            setTimeout(checkAppLoaded, 1000);
        };
    </script>
</body>
</html>
